/* This file is automatically generated. DO NOT EDIT it manually. */

/* plain colors  */
@Light-Violet-1: var(--Light-Violet-1);
@Light-Violet-2: var(--Light-Violet-2);
@Light-Violet-3: var(--Light-Violet-3);
@Light-Violet-4: var(--Light-Violet-4);
@Light-Violet-5: var(--Light-Violet-5);
@Light-Violet-6: var(--Light-Violet-6);
@Light-Violet-7: var(--Light-Violet-7);
@Light-Violet-8: var(--Light-Violet-8);
@Light-Violet-9: var(--Light-Violet-9);
@Light-Violet-10: var(--Light-Violet-10);
@Light-Blue-1: var(--Light-Blue-1);
@Light-Blue-2: var(--Light-Blue-2);
@Light-Blue-3: var(--Light-Blue-3);
@Light-Blue-4: var(--Light-Blue-4);
@Light-Blue-5: var(--Light-Blue-5);
@Light-Blue-6: var(--Light-Blue-6);
@Light-Blue-7: var(--Light-Blue-7);
@Light-Blue-8: var(--Light-Blue-8);
@Light-Blue-9: var(--Light-Blue-9);
@Light-Blue-10: var(--Light-Blue-10);
@Light-Lightblue-1: var(--Light-Lightblue-1);
@Light-Lightblue-2: var(--Light-Lightblue-2);
@Light-Lightblue-3: var(--Light-Lightblue-3);
@Light-Lightblue-4: var(--Light-Lightblue-4);
@Light-Lightblue-5: var(--Light-Lightblue-5);
@Light-Lightblue-6: var(--Light-Lightblue-6);
@Light-Lightblue-7: var(--Light-Lightblue-7);
@Light-Lightblue-8: var(--Light-Lightblue-8);
@Light-Lightblue-9: var(--Light-Lightblue-9);
@Light-Lightblue-10: var(--Light-Lightblue-10);
@Light-Teal-1: var(--Light-Teal-1);
@Light-Teal-2: var(--Light-Teal-2);
@Light-Teal-3: var(--Light-Teal-3);
@Light-Teal-4: var(--Light-Teal-4);
@Light-Teal-5: var(--Light-Teal-5);
@Light-Teal-6: var(--Light-Teal-6);
@Light-Teal-7: var(--Light-Teal-7);
@Light-Teal-8: var(--Light-Teal-8);
@Light-Teal-9: var(--Light-Teal-9);
@Light-Teal-10: var(--Light-Teal-10);
@Light-Green-1: var(--Light-Green-1);
@Light-Green-2: var(--Light-Green-2);
@Light-Green-3: var(--Light-Green-3);
@Light-Green-4: var(--Light-Green-4);
@Light-Green-5: var(--Light-Green-5);
@Light-Green-6: var(--Light-Green-6);
@Light-Green-7: var(--Light-Green-7);
@Light-Green-8: var(--Light-Green-8);
@Light-Green-9: var(--Light-Green-9);
@Light-Green-10: var(--Light-Green-10);
@Light-Lightgreen-1: var(--Light-Lightgreen-1);
@Light-Lightgreen-2: var(--Light-Lightgreen-2);
@Light-Lightgreen-3: var(--Light-Lightgreen-3);
@Light-Lightgreen-4: var(--Light-Lightgreen-4);
@Light-Lightgreen-5: var(--Light-Lightgreen-5);
@Light-Lightgreen-6: var(--Light-Lightgreen-6);
@Light-Lightgreen-7: var(--Light-Lightgreen-7);
@Light-Lightgreen-8: var(--Light-Lightgreen-8);
@Light-Lightgreen-9: var(--Light-Lightgreen-9);
@Light-Lightgreen-10: var(--Light-Lightgreen-10);
@Light-Yellow-1: var(--Light-Yellow-1);
@Light-Yellow-2: var(--Light-Yellow-2);
@Light-Yellow-3: var(--Light-Yellow-3);
@Light-Yellow-4: var(--Light-Yellow-4);
@Light-Yellow-5: var(--Light-Yellow-5);
@Light-Yellow-6: var(--Light-Yellow-6);
@Light-Yellow-7: var(--Light-Yellow-7);
@Light-Yellow-8: var(--Light-Yellow-8);
@Light-Yellow-9: var(--Light-Yellow-9);
@Light-Yellow-10: var(--Light-Yellow-10);
@Light-Orange-1: var(--Light-Orange-1);
@Light-Orange-2: var(--Light-Orange-2);
@Light-Orange-3: var(--Light-Orange-3);
@Light-Orange-4: var(--Light-Orange-4);
@Light-Orange-5: var(--Light-Orange-5);
@Light-Orange-6: var(--Light-Orange-6);
@Light-Orange-7: var(--Light-Orange-7);
@Light-Orange-8: var(--Light-Orange-8);
@Light-Orange-9: var(--Light-Orange-9);
@Light-Orange-10: var(--Light-Orange-10);
@Light-Red-1: var(--Light-Red-1);
@Light-Red-2: var(--Light-Red-2);
@Light-Red-3: var(--Light-Red-3);
@Light-Red-4: var(--Light-Red-4);
@Light-Red-5: var(--Light-Red-5);
@Light-Red-6: var(--Light-Red-6);
@Light-Red-7: var(--Light-Red-7);
@Light-Red-8: var(--Light-Red-8);
@Light-Red-9: var(--Light-Red-9);
@Light-Red-10: var(--Light-Red-10);
@Light-Pink-1: var(--Light-Pink-1);
@Light-Pink-2: var(--Light-Pink-2);
@Light-Pink-3: var(--Light-Pink-3);
@Light-Pink-4: var(--Light-Pink-4);
@Light-Pink-5: var(--Light-Pink-5);
@Light-Pink-6: var(--Light-Pink-6);
@Light-Pink-7: var(--Light-Pink-7);
@Light-Pink-8: var(--Light-Pink-8);
@Light-Pink-9: var(--Light-Pink-9);
@Light-Pink-10: var(--Light-Pink-10);
@Light-Purple-1: var(--Light-Purple-1);
@Light-Purple-2: var(--Light-Purple-2);
@Light-Purple-3: var(--Light-Purple-3);
@Light-Purple-4: var(--Light-Purple-4);
@Light-Purple-5: var(--Light-Purple-5);
@Light-Purple-6: var(--Light-Purple-6);
@Light-Purple-7: var(--Light-Purple-7);
@Light-Purple-8: var(--Light-Purple-8);
@Light-Purple-9: var(--Light-Purple-9);
@Light-Purple-10: var(--Light-Purple-10);
@Light-Gray-0: var(--Light-Gray-0);
@Light-Gray-1: var(--Light-Gray-1);
@Light-Gray-2: var(--Light-Gray-2);
@Light-Gray-3: var(--Light-Gray-3);
@Light-Gray-4: var(--Light-Gray-4);
@Light-Gray-5: var(--Light-Gray-5);
@Light-Gray-6: var(--Light-Gray-6);
@Light-Gray-7: var(--Light-Gray-7);
@Light-Gray-8: var(--Light-Gray-8);
@Light-Gray-9: var(--Light-Gray-9);
@Light-Gray-10: var(--Light-Gray-10);
@Light-Gray-11: var(--Light-Gray-11);
@Light-Gray-12: var(--Light-Gray-12);
@Light-Gray-13: var(--Light-Gray-13);
@Light-Gray-14: var(--Light-Gray-14);
@Light-Gray-15: var(--Light-Gray-15);
@Light-Gray-16: var(--Light-Gray-16);
@Dark-Violet-1: var(--Dark-Violet-1);
@Dark-Violet-2: var(--Dark-Violet-2);
@Dark-Violet-3: var(--Dark-Violet-3);
@Dark-Violet-4: var(--Dark-Violet-4);
@Dark-Violet-5: var(--Dark-Violet-5);
@Dark-Violet-6: var(--Dark-Violet-6);
@Dark-Violet-7: var(--Dark-Violet-7);
@Dark-Violet-8: var(--Dark-Violet-8);
@Dark-Violet-9: var(--Dark-Violet-9);
@Dark-Violet-10: var(--Dark-Violet-10);
@Dark-Blue-1: var(--Dark-Blue-1);
@Dark-Blue-2: var(--Dark-Blue-2);
@Dark-Blue-3: var(--Dark-Blue-3);
@Dark-Blue-4: var(--Dark-Blue-4);
@Dark-Blue-5: var(--Dark-Blue-5);
@Dark-Blue-6: var(--Dark-Blue-6);
@Dark-Blue-7: var(--Dark-Blue-7);
@Dark-Blue-8: var(--Dark-Blue-8);
@Dark-Blue-9: var(--Dark-Blue-9);
@Dark-Blue-10: var(--Dark-Blue-10);
@Dark-Lightblue-1: var(--Dark-Lightblue-1);
@Dark-Lightblue-2: var(--Dark-Lightblue-2);
@Dark-Lightblue-3: var(--Dark-Lightblue-3);
@Dark-Lightblue-4: var(--Dark-Lightblue-4);
@Dark-Lightblue-5: var(--Dark-Lightblue-5);
@Dark-Lightblue-6: var(--Dark-Lightblue-6);
@Dark-Lightblue-7: var(--Dark-Lightblue-7);
@Dark-Lightblue-8: var(--Dark-Lightblue-8);
@Dark-Lightblue-9: var(--Dark-Lightblue-9);
@Dark-Lightblue-10: var(--Dark-Lightblue-10);
@Dark-Teal-1: var(--Dark-Teal-1);
@Dark-Teal-2: var(--Dark-Teal-2);
@Dark-Teal-3: var(--Dark-Teal-3);
@Dark-Teal-4: var(--Dark-Teal-4);
@Dark-Teal-5: var(--Dark-Teal-5);
@Dark-Teal-6: var(--Dark-Teal-6);
@Dark-Teal-7: var(--Dark-Teal-7);
@Dark-Teal-8: var(--Dark-Teal-8);
@Dark-Teal-9: var(--Dark-Teal-9);
@Dark-Teal-10: var(--Dark-Teal-10);
@Dark-Green-1: var(--Dark-Green-1);
@Dark-Green-2: var(--Dark-Green-2);
@Dark-Green-3: var(--Dark-Green-3);
@Dark-Green-4: var(--Dark-Green-4);
@Dark-Green-5: var(--Dark-Green-5);
@Dark-Green-6: var(--Dark-Green-6);
@Dark-Green-7: var(--Dark-Green-7);
@Dark-Green-8: var(--Dark-Green-8);
@Dark-Green-9: var(--Dark-Green-9);
@Dark-Green-10: var(--Dark-Green-10);
@Dark-Lightgreen-1: var(--Dark-Lightgreen-1);
@Dark-Lightgreen-2: var(--Dark-Lightgreen-2);
@Dark-Lightgreen-3: var(--Dark-Lightgreen-3);
@Dark-Lightgreen-4: var(--Dark-Lightgreen-4);
@Dark-Lightgreen-5: var(--Dark-Lightgreen-5);
@Dark-Lightgreen-6: var(--Dark-Lightgreen-6);
@Dark-Lightgreen-7: var(--Dark-Lightgreen-7);
@Dark-Lightgreen-8: var(--Dark-Lightgreen-8);
@Dark-Lightgreen-9: var(--Dark-Lightgreen-9);
@Dark-Lightgreen-10: var(--Dark-Lightgreen-10);
@Dark-Yellow-1: var(--Dark-Yellow-1);
@Dark-Yellow-2: var(--Dark-Yellow-2);
@Dark-Yellow-3: var(--Dark-Yellow-3);
@Dark-Yellow-4: var(--Dark-Yellow-4);
@Dark-Yellow-5: var(--Dark-Yellow-5);
@Dark-Yellow-6: var(--Dark-Yellow-6);
@Dark-Yellow-7: var(--Dark-Yellow-7);
@Dark-Yellow-8: var(--Dark-Yellow-8);
@Dark-Yellow-9: var(--Dark-Yellow-9);
@Dark-Yellow-10: var(--Dark-Yellow-10);
@Dark-Orange-1: var(--Dark-Orange-1);
@Dark-Orange-2: var(--Dark-Orange-2);
@Dark-Orange-3: var(--Dark-Orange-3);
@Dark-Orange-4: var(--Dark-Orange-4);
@Dark-Orange-5: var(--Dark-Orange-5);
@Dark-Orange-6: var(--Dark-Orange-6);
@Dark-Orange-7: var(--Dark-Orange-7);
@Dark-Orange-8: var(--Dark-Orange-8);
@Dark-Orange-9: var(--Dark-Orange-9);
@Dark-Orange-10: var(--Dark-Orange-10);
@Dark-Red-1: var(--Dark-Red-1);
@Dark-Red-2: var(--Dark-Red-2);
@Dark-Red-3: var(--Dark-Red-3);
@Dark-Red-4: var(--Dark-Red-4);
@Dark-Red-5: var(--Dark-Red-5);
@Dark-Red-6: var(--Dark-Red-6);
@Dark-Red-7: var(--Dark-Red-7);
@Dark-Red-8: var(--Dark-Red-8);
@Dark-Red-9: var(--Dark-Red-9);
@Dark-Red-10: var(--Dark-Red-10);
@Dark-Pink-1: var(--Dark-Pink-1);
@Dark-Pink-2: var(--Dark-Pink-2);
@Dark-Pink-3: var(--Dark-Pink-3);
@Dark-Pink-4: var(--Dark-Pink-4);
@Dark-Pink-5: var(--Dark-Pink-5);
@Dark-Pink-6: var(--Dark-Pink-6);
@Dark-Pink-7: var(--Dark-Pink-7);
@Dark-Pink-8: var(--Dark-Pink-8);
@Dark-Pink-9: var(--Dark-Pink-9);
@Dark-Pink-10: var(--Dark-Pink-10);
@Dark-Purple-1: var(--Dark-Purple-1);
@Dark-Purple-2: var(--Dark-Purple-2);
@Dark-Purple-3: var(--Dark-Purple-3);
@Dark-Purple-4: var(--Dark-Purple-4);
@Dark-Purple-5: var(--Dark-Purple-5);
@Dark-Purple-6: var(--Dark-Purple-6);
@Dark-Purple-7: var(--Dark-Purple-7);
@Dark-Purple-8: var(--Dark-Purple-8);
@Dark-Purple-9: var(--Dark-Purple-9);
@Dark-Purple-10: var(--Dark-Purple-10);
@Dark-Gray-0: var(--Dark-Gray-0);
@Dark-Gray-1: var(--Dark-Gray-1);
@Dark-Gray-2: var(--Dark-Gray-2);
@Dark-Gray-3: var(--Dark-Gray-3);
@Dark-Gray-4: var(--Dark-Gray-4);
@Dark-Gray-5: var(--Dark-Gray-5);
@Dark-Gray-6: var(--Dark-Gray-6);
@Dark-Gray-7: var(--Dark-Gray-7);
@Dark-Gray-8: var(--Dark-Gray-8);
@Dark-Gray-9: var(--Dark-Gray-9);
@Dark-Gray-10: var(--Dark-Gray-10);
@Dark-Gray-11: var(--Dark-Gray-11);
@Dark-Gray-12: var(--Dark-Gray-12);
@Dark-Gray-13: var(--Dark-Gray-13);
@Dark-Gray-14: var(--Dark-Gray-14);
@Dark-Gray-15: var(--Dark-Gray-15);
@Dark-Gray-16: var(--Dark-Gray-16);
@Basic-Black-0: var(--Basic-Black-0);
@Basic-Black-5: var(--Basic-Black-5);
@Basic-Black-10: var(--Basic-Black-10);
@Basic-Black-20: var(--Basic-Black-20);
@Basic-Black-30: var(--Basic-Black-30);
@Basic-Black-40: var(--Basic-Black-40);
@Basic-Black-50: var(--Basic-Black-50);
@Basic-Black-60: var(--Basic-Black-60);
@Basic-Black-70: var(--Basic-Black-70);
@Basic-Black-80: var(--Basic-Black-80);
@Basic-Black-90: var(--Basic-Black-90);
@Basic-Black-100: var(--Basic-Black-100);
@Basic-White-0: var(--Basic-White-0);
@Basic-White-5: var(--Basic-White-5);
@Basic-White-10: var(--Basic-White-10);
@Basic-White-20: var(--Basic-White-20);
@Basic-White-30: var(--Basic-White-30);
@Basic-White-40: var(--Basic-White-40);
@Basic-White-50: var(--Basic-White-50);
@Basic-White-60: var(--Basic-White-60);
@Basic-White-70: var(--Basic-White-70);
@Basic-White-80: var(--Basic-White-80);
@Basic-White-90: var(--Basic-White-90);
@Basic-White-100: var(--Basic-White-100);
/* color vars  */
@FO-Brand-Primary-Default: var(--FO-Brand-Primary-Default);
@FO-Brand-Primary-Hover: var(--FO-Brand-Primary-Hover);
@FO-Brand-Primary-Active: var(--FO-Brand-Primary-Active);
@FO-Brand-Primary-Disabled: var(--FO-Brand-Primary-Disabled);
@FO-Brand-Secondary-Default: var(--FO-Brand-Secondary-Default);
@FO-Brand-Secondary-Hover: var(--FO-Brand-Secondary-Hover);
@FO-Brand-Secondary-Active: var(--FO-Brand-Secondary-Active);
@FO-Brand-Secondary-Disabled: var(--FO-Brand-Secondary-Disabled);
@FO-Brand-Tertiary-Active: var(--FO-Brand-Tertiary-Active);
@FO-Functional-Success1-Default: var(--FO-Functional-Success1-Default);
@FO-Functional-Success1-Hover: var(--FO-Functional-Success1-Hover);
@FO-Functional-Success1-Active: var(--FO-Functional-Success1-Active);
@FO-Functional-Success1-Disabled: var(--FO-Functional-Success1-Disabled);
@FO-Functional-Success2-Default: var(--FO-Functional-Success2-Default);
@FO-Functional-Success2-Hover: var(--FO-Functional-Success2-Hover);
@FO-Functional-Success2-Active: var(--FO-Functional-Success2-Active);
@FO-Functional-Success2-Disabled: var(--FO-Functional-Success2-Disabled);
@FO-Functional-Warning1-Default: var(--FO-Functional-Warning1-Default);
@FO-Functional-Warning1-Hover: var(--FO-Functional-Warning1-Hover);
@FO-Functional-Warning1-Active: var(--FO-Functional-Warning1-Active);
@FO-Functional-Warning1-Disabled: var(--FO-Functional-Warning1-Disabled);
@FO-Functional-Warning2-Default: var(--FO-Functional-Warning2-Default);
@FO-Functional-Warning2-Hover: var(--FO-Functional-Warning2-Hover);
@FO-Functional-Warning2-Active: var(--FO-Functional-Warning2-Active);
@FO-Functional-Warning2-Disabled: var(--FO-Functional-Warning2-Disabled);
@FO-Functional-Error1-Default: var(--FO-Functional-Error1-Default);
@FO-Functional-Error1-Hover: var(--FO-Functional-Error1-Hover);
@FO-Functional-Error1-Active: var(--FO-Functional-Error1-Active);
@FO-Functional-Error1-Disabled: var(--FO-Functional-Error1-Disabled);
@FO-Functional-Error2-Default: var(--FO-Functional-Error2-Default);
@FO-Functional-Error2-Hover: var(--FO-Functional-Error2-Hover);
@FO-Functional-Error2-Active: var(--FO-Functional-Error2-Active);
@FO-Functional-Error2-Disabled: var(--FO-Functional-Error2-Disabled);
@FO-Functional-Info1-Default: var(--FO-Functional-Info1-Default);
@FO-Functional-Info1-Hover: var(--FO-Functional-Info1-Hover);
@FO-Functional-Info1-Active: var(--FO-Functional-Info1-Active);
@FO-Functional-Info1-Disabled: var(--FO-Functional-Info1-Disabled);
@FO-Functional-Info2-Default: var(--FO-Functional-Info2-Default);
@FO-Functional-Info2-Hover: var(--FO-Functional-Info2-Hover);
@FO-Functional-Info2-Active: var(--FO-Functional-Info2-Active);
@FO-Functional-Info2-Disabled: var(--FO-Functional-Info2-Disabled);
@FO-Content-Text0: var(--FO-Content-Text0);
@FO-Content-Text1: var(--FO-Content-Text1);
@FO-Content-Text2: var(--FO-Content-Text2);
@FO-Content-Text3: var(--FO-Content-Text3);
@FO-Content-Text4: var(--FO-Content-Text4);
@FO-Content-Icon0: var(--FO-Content-Icon0);
@FO-Content-Icon1: var(--FO-Content-Icon1);
@FO-Content-Icon2: var(--FO-Content-Icon2);
@FO-Content-Icon3: var(--FO-Content-Icon3);
@FO-Content-Icon4: var(--FO-Content-Icon4);
@FO-Content-Components1: var(--FO-Content-Components1);
@FO-Content-Components2: var(--FO-Content-Components2);
@FO-Content-Link-Default: var(--FO-Content-Link-Default);
@FO-Content-Link-Hover: var(--FO-Content-Link-Hover);
@FO-Content-Link-Active: var(--FO-Content-Link-Active);
@FO-Content-Link-Disabled: var(--FO-Content-Link-Disabled);
@FO-Container-Mask0: var(--FO-Container-Mask0);
@FO-Container-Mask1: var(--FO-Container-Mask1);
@FO-Container-Background: var(--FO-Container-Background);
@FO-Container-Background2: var(--FO-Container-Background2);
@FO-Container-Fill0: var(--FO-Container-Fill0);
@FO-Container-Fill1: var(--FO-Container-Fill1);
@FO-Container-Fill2: var(--FO-Container-Fill2);
@FO-Container-Fill3: var(--FO-Container-Fill3);
@FO-Container-Fill4: var(--FO-Container-Fill4);
@FO-Container-Fill5: var(--FO-Container-Fill5);
@FO-Container-Fill6: var(--FO-Container-Fill6);
@FO-Container-Stroke0: var(--FO-Container-Stroke0);
@FO-Container-Stroke1: var(--FO-Container-Stroke1);
@FO-Container-Stroke2: var(--FO-Container-Stroke2);
@FO-Container-Stroke3: var(--FO-Container-Stroke3);
@FO-Container-Stroke4: var(--FO-Container-Stroke4);
@FO-Container-Stroke5: var(--FO-Container-Stroke5);
@FO-Datavis-Violet1: var(--FO-Datavis-Violet1);
@FO-Datavis-Violet2: var(--FO-Datavis-Violet2);
@FO-Datavis-Violet3: var(--FO-Datavis-Violet3);
@FO-Datavis-Blue1: var(--FO-Datavis-Blue1);
@FO-Datavis-Blue2: var(--FO-Datavis-Blue2);
@FO-Datavis-Blue3: var(--FO-Datavis-Blue3);
@FO-Datavis-Lightblue1: var(--FO-Datavis-Lightblue1);
@FO-Datavis-Lightblue2: var(--FO-Datavis-Lightblue2);
@FO-Datavis-Lightblue3: var(--FO-Datavis-Lightblue3);
@FO-Datavis-Teal1: var(--FO-Datavis-Teal1);
@FO-Datavis-Teal2: var(--FO-Datavis-Teal2);
@FO-Datavis-Teal3: var(--FO-Datavis-Teal3);
@FO-Datavis-Green1: var(--FO-Datavis-Green1);
@FO-Datavis-Green2: var(--FO-Datavis-Green2);
@FO-Datavis-Green3: var(--FO-Datavis-Green3);
@FO-Datavis-Lightgreen1: var(--FO-Datavis-Lightgreen1);
@FO-Datavis-Lightgreen2: var(--FO-Datavis-Lightgreen2);
@FO-Datavis-Lightgreen3: var(--FO-Datavis-Lightgreen3);
@FO-Datavis-Yellow1: var(--FO-Datavis-Yellow1);
@FO-Datavis-Yellow2: var(--FO-Datavis-Yellow2);
@FO-Datavis-Yellow3: var(--FO-Datavis-Yellow3);
@FO-Datavis-Orange1: var(--FO-Datavis-Orange1);
@FO-Datavis-Orange2: var(--FO-Datavis-Orange2);
@FO-Datavis-Orange3: var(--FO-Datavis-Orange3);
@FO-Datavis-Red1: var(--FO-Datavis-Red1);
@FO-Datavis-Red2: var(--FO-Datavis-Red2);
@FO-Datavis-Red3: var(--FO-Datavis-Red3);
@FO-Datavis-Pink1: var(--FO-Datavis-Pink1);
@FO-Datavis-Pink2: var(--FO-Datavis-Pink2);
@FO-Datavis-Pink3: var(--FO-Datavis-Pink3);
@FO-Datavis-Purple1: var(--FO-Datavis-Purple1);
@FO-Datavis-Purple2: var(--FO-Datavis-Purple2);
@FO-Datavis-Purple3: var(--FO-Datavis-Purple3);

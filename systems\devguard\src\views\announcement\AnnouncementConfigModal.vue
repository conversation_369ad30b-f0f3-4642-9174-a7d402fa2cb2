<template>
  <Modal
    :width="700"
    :open="show"
    :maskClosable="false"
    :footer="null"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>提交公告配置</span>
        </span>
      </div>
    </template>
    <div v-if="!isEdit">
      <div class="relative">
        <Tabs v-model:activeKey="announcementType">
          <TabPane key="new" tab="新公告">
            <div class="h-400px overflow-y-auto">
              <div v-if="announcementsList.length">
                <AnnouncementItem v-for="item in announcementsList" :key="item.ID" :item="item" :streamsList="streamsList" announcementType="new" @editAnnouncement="editAnnouncement" />
              </div>
              <div v-else class="h-full flex items-center justify-center c-FO-Container-Fill6">
                暂无新公告
              </div>
            </div>
          </TabPane>
          <TabPane key="history" tab="历史公告">
            <div class="h-400px overflow-y-auto">
              <div v-if="announcementsHistoryList.length">
                <AnnouncementItem v-for="item in announcementsHistoryList" :key="item.ID" :item="item" :streamsList="streamsList" announcementType="history" @reuseAnnouncement="reuseAnnouncement" />
              </div>
              <div v-else class="h-full flex items-center justify-center c-FO-Container-Fill6">
                暂无新公告
              </div>
            </div>
          </TabPane>
        </Tabs>
        <div class="absolute right-0 top-12px">
          <Button shape="round" size="small" @click="isEdit = true">
            <div class="flex items-center gap-4px">
              <Icon :icon="addIcon" />
              <span>新增公告</span>
            </div>
          </Button>
        </div>
      </div>
    </div>
    <div v-else>
      <Button shape="round" size="small" @click="handleBack">
        <div class="flex items-center gap-4px">
          <Icon :icon="LeftIcon" />
          <span>返回</span>
        </div>
      </Button>
      <EditAnnouncement ref="editAnnouncementRef" class="h-300px" :streamsList="streamsList" />
      <div class="flex justify-center">
        <Button type="primary" @click="handleSave">
          保存
        </Button>
        <Button class="ml-2 bg-FO-Functional-Error1-Default!" type="primary" @click="handleBack">
          取消
        </Button>
      </div>
    </div>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Modal, TabPane, Tabs } from 'ant-design-vue';
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import AnnouncementItem from './AnnouncementItem.vue';
import { Icon } from '@iconify/vue';
import LeftIcon from '@iconify-icons/icon-park-outline/left';
import addIcon from '@iconify-icons/icon-park-outline/plus';
import type { NullableBasicResult } from '/@/api/model/baseModel';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import EditAnnouncement from './EditAnnouncement.vue';
import { type AnnouncementsItem, type StreamsListItem, addAnnouncementsHistoryListApi, getAnnouncementsHistoryListApi, getAnnouncementsListApi, getListSubmit, updateAnnouncementsHistoryListApi } from '../../api';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { omit } from 'lodash';

defineProps< ModalBaseProps<{ updatedItem?: NullableBasicResult }> & {

}>();
const forgeonConfig = useForgeonConfigStore(store);
const { execute: getAnnouncementsListExecute, data: announcementsListData } = useLatestPromise(getAnnouncementsListApi);
const { execute: getAnnouncementsHistoryListExecute, data: announcementsHistoryListData } = useLatestPromise(getAnnouncementsHistoryListApi);
const { execute: addAnnouncementsHistoryListExecute } = useLatestPromise(addAnnouncementsHistoryListApi);
const { execute: updateAnnouncementsHistoryListExecute } = useLatestPromise(updateAnnouncementsHistoryListApi);

const { execute: getListSubmitExecute, data: submitList } = useLatestPromise(getListSubmit);
const currentProjectId = computed(() => forgeonConfig.currentProjectId);
const projectCode = computed(() => forgeonConfig.currentProjectInfo?.alias);
const announcementType = ref('new');
const streamsList = ref< StreamsListItem[]>([]);
const isEdit = ref(false);
const editAnnouncementRef = ref();
const isUpdate = ref(false);
const announcementsId = ref<number>();
const announcementsList = computed(() => announcementsListData.value?.data?.data || []);
const announcementsHistoryList = computed(() => announcementsHistoryListData.value?.data?.data || []);
async function handleBack() {
  isEdit.value = false;
  isUpdate.value = false;
}

async function handleSave() {
  const params = await editAnnouncementRef.value.getParams();
  if (isUpdate.value) {
    await updateAnnouncementsHistoryListExecute({ id: currentProjectId.value! }, { ...params, recordID: announcementsId.value! });
  } else {
    await addAnnouncementsHistoryListExecute({ id: currentProjectId.value! }, params);
  }
  handleBack();
  getData();
}

function editAnnouncement(item: AnnouncementsItem) {
  isEdit.value = true;
  isUpdate.value = true;
  announcementsId.value = item.ID;
  nextTick(() => {
    editAnnouncementRef.value.setParams(item);
  });
}
async function reuseAnnouncement(item: AnnouncementsItem) {
  await addAnnouncementsHistoryListExecute({ id: currentProjectId.value! }, omit(item, ['ID', 'inEffect', 'modifier', 'CreatedAt', 'UpdatedAt']));
  getData();
}

function getData() {
  if (announcementType.value === 'new') {
    getAnnouncementsListExecute({ id: currentProjectId.value! }, {});
  } else {
    getAnnouncementsHistoryListExecute({ id: currentProjectId.value! }, {});
  }
}

onMounted(async () => {
  await getListSubmitExecute({ projectCode: projectCode.value!, id: currentProjectId.value! }, {});
  streamsList.value = submitList.value?.data?.data?.list.map((item) => item.streams).flat() || [];
});

watch(() => announcementType.value, () => {
  getData();
}, {
  immediate: true,
  deep: true,
});
</script>

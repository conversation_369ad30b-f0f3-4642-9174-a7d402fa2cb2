<template>
  <div ref="containerRef" class="flex flex-col transition-all transition-duration-300">
    {{ announcementsItem }}
  </div>
</template>

<script lang="ts" setup>
import type { showAnnouncementsItem } from '../../api';
import { computed, ref } from 'vue';

const props = withDefaults(defineProps<{
  nowAnnouncementsItem: showAnnouncementsItem;
}>(), {
  nowAnnouncementsItem: () => ({} as showAnnouncementsItem),
});
const containerRef = ref<HTMLElement>();
const announcementsItem = computed(() => props.nowAnnouncementsItem);
// });
</script>

<style lang="less" scoped>

</style>
